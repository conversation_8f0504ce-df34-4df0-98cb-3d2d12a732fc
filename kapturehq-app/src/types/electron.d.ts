export interface VaultData {
  name: string;
  type: 'Project' | 'Lead';
  folderPath: string;
}

export interface Vault {
  name: string;
  type: 'Project' | 'Lead';
  path: string;
  createdAt: string;
}

export interface ElectronAPI {
  selectFolder: () => Promise<string | null>;
  createVault: (vaultData: VaultData) => Promise<{ success: boolean; path?: string; error?: string }>;
  getVaults: () => Promise<Vault[]>;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
