const { app, BrowserWindow, dialog, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

const isDev = process.env.NODE_ENV === 'development';

function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    titleBarStyle: 'hiddenInset',
    show: false
  });

  // Load the app
  if (isDev) {
    mainWindow.loadURL('http://localhost:3000');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../out/index.html'));
  }

  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  return mainWindow;
}

// Handle vault folder selection
ipcMain.handle('select-folder', async () => {
  const result = await dialog.showOpenDialog({
    properties: ['openDirectory'],
    title: 'Select Vault Folder'
  });

  if (!result.canceled && result.filePaths.length > 0) {
    return result.filePaths[0];
  }
  return null;
});

// Handle vault creation
ipcMain.handle('create-vault', async (event, vaultData) => {
  try {
    const { name, type, folderPath } = vaultData;
    const vaultPath = path.join(folderPath, name);

    // Create vault directory
    if (!fs.existsSync(vaultPath)) {
      fs.mkdirSync(vaultPath, { recursive: true });
    }

    // Create vault metadata file
    const metadata = {
      name,
      type,
      createdAt: new Date().toISOString(),
      version: '1.0.0'
    };

    fs.writeFileSync(
      path.join(vaultPath, '.vault-metadata.json'),
      JSON.stringify(metadata, null, 2)
    );

    // Create initial folders
    fs.mkdirSync(path.join(vaultPath, 'projects'), { recursive: true });
    fs.mkdirSync(path.join(vaultPath, 'notes'), { recursive: true });

    return { success: true, path: vaultPath };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Handle getting existing vaults
ipcMain.handle('get-vaults', async () => {
  try {
    // This would typically read from a config file or registry
    // For now, return empty array
    return [];
  } catch (error) {
    return [];
  }
});

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
