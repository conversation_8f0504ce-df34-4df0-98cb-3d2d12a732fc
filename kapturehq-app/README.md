# KaptureHQ

A desktop project management app inspired by Obsidian, built with Electron.js and Next.js.

## Features

- **Onboarding Screen**: Clean, modern interface for vault management
- **Vault Management**: Create and manage project vaults locally
- **Desktop App**: Native desktop experience with Electron
- **Modern UI**: Built with Tailwind CSS, ShadCN UI components, and Lucide icons
- **Outfit Font**: Professional typography throughout the app

## Tech Stack

- **Frontend**: Next.js 15 with TypeScript
- **Desktop**: Electron.js
- **Styling**: Tailwind CSS v4
- **Components**: ShadCN UI
- **Icons**: Lucide React
- **Font**: Outfit (Google Fonts)

## Development

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

```bash
# Install dependencies
npm install
```

### Running the App

#### Web Development
```bash
npm run dev
```
Open [http://localhost:3000](http://localhost:3000) in your browser.

#### Electron Development
```bash
npm run electron-dev
```
This will start both the Next.js dev server and the Electron app.

#### Electron Only
```bash
npm run electron
```

### Building

#### Build for Web
```bash
npm run build
```

#### Build Electron App
```bash
npm run build-electron
```

#### Create Distribution
```bash
npm run dist
```

## Project Structure

```
kapturehq-app/
├── electron/
│   ├── main.js          # Electron main process
│   └── preload.js       # Electron preload script
├── src/
│   ├── app/             # Next.js app directory
│   ├── components/      # React components
│   │   ├── ui/          # ShadCN UI components
│   │   └── OnboardingScreen.tsx
│   └── types/           # TypeScript type definitions
├── public/              # Static assets
└── package.json
```

## Features Overview

### Onboarding Screen

The main onboarding interface features:

- **Left Panel**: "Your Vaults" - Lists existing vaults with:
  - Vault name and type (Project/Lead) badges
  - Folder path display
  - Open and delete actions
  - Empty state with helpful messaging

- **Right Panel**: "Create New Vault" - Form with:
  - Vault name input
  - Vault type dropdown (Project/Lead)
  - Folder path selection with file picker
  - Create vault button

- **Additional Features**:
  - Theme toggle (light/dark mode)
  - Responsive 2-panel layout
  - Hover effects and smooth transitions
  - Disabled states for web version

### Vault Management

- Vaults are stored as local folders
- Each vault contains CSV and MD files
- Metadata stored in `.vault-metadata.json`
- Automatic folder structure creation
